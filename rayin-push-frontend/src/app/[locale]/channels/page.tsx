'use client'

import { useState } from 'react'
import { AppLayout } from '@/components/app-layout'
import { useClientTranslation } from '@/hooks/useClientTranslation'
import { useStoreInitialization } from '@/hooks/useStore'
import { ChannelList } from '@/components/channels/channel-list'
import { ChannelToolbar } from '@/components/channels/channel-toolbar'
import { ChannelForm } from '@/components/channels/channel-form'
import { mockNotificationChannels } from '@/mock/channels'
import type { NotificationChannel } from '@/types/data'

export default function ChannelsPage() {
  const { t } = useClientTranslation('channels')
  const { isHydrated } = useStoreInitialization()
  const [channels, setChannels] = useState<NotificationChannel[]>(mockNotificationChannels)
  const [selectedChannels, setSelectedChannels] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all')
  const [typeFilter, setTypeFilter] = useState<'all' | 'wechat' | 'feishu' | 'webhook'>('all')
  const [createModalOpen, setCreateModalOpen] = useState(false)

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  // 过滤渠道数据
  const filteredChannels = channels.filter(channel => {
    const matchesSearch = channel.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      channel.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = statusFilter === 'all' || channel.status === statusFilter
    const matchesType = typeFilter === 'all' || channel.type === typeFilter
    return matchesSearch && matchesStatus && matchesType
  })

  // 分页数据
  const total = filteredChannels.length
  const startIndex = (currentPage - 1) * pageSize
  const endIndex = startIndex + pageSize
  const paginatedChannels = filteredChannels.slice(startIndex, endIndex)

  // 处理渠道状态切换
  const handleToggleStatus = (id: string) => {
    setChannels(prev => prev.map(channel =>
      channel.id === id
        ? { ...channel, status: channel.status === 'active' ? 'inactive' : 'active', updatedTime: new Date().toISOString() }
        : channel
    ))
  }

  // 处理更新渠道
  const handleUpdateChannel = (updatedChannel: NotificationChannel) => {
    setChannels(prev => prev.map(channel =>
      channel.id === updatedChannel.id ? updatedChannel : channel
    ))
  }

  // 处理删除渠道
  const handleDeleteChannel = (id: string) => {
    setChannels(prev => prev.filter(channel => channel.id !== id))
    setSelectedChannels(prev => prev.filter(channelId => channelId !== id))
  }

  // 处理创建渠道
  const handleCreateChannel = (newChannelData: Partial<NotificationChannel>) => {
    const newChannel: NotificationChannel = {
      id: (channels.length + 1).toString(),
      name: newChannelData.name!,
      type: newChannelData.type!,
      description: newChannelData.description!,
      status: 'active',
      config: newChannelData.config!,
      createdTime: new Date().toISOString(),
      updatedTime: new Date().toISOString(),
      creator: 'current_user'
    }
    setChannels(prev => [...prev, newChannel])
    setCreateModalOpen(false)
  }

  // 处理批量删除
  const handleBatchDelete = () => {
    setChannels(prev => prev.filter(channel => !selectedChannels.includes(channel.id)))
    setSelectedChannels([])
  }

  // 处理批量状态切换
  const handleBatchToggleStatus = (status: 'active' | 'inactive') => {
    setChannels(prev => prev.map(channel =>
      selectedChannels.includes(channel.id)
        ? { ...channel, status, updatedTime: new Date().toISOString() }
        : channel
    ))
    setSelectedChannels([])
  }

  // 处理页码变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    setSelectedChannels([]) // 切换页面时清空选择
  }

  // 处理每页数量变化
  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize)
    setCurrentPage(1) // 重置到第一页
    setSelectedChannels([]) // 清空选择
  }

  // 当搜索或筛选条件变化时重置到第一页
  const handleSearchChange = (query: string) => {
    setSearchQuery(query)
    setCurrentPage(1)
    setSelectedChannels([])
  }

  const handleStatusFilterChange = (status: 'all' | 'active' | 'inactive') => {
    setStatusFilter(status)
    setCurrentPage(1)
    setSelectedChannels([])
  }

  const handleTypeFilterChange = (type: 'all' | 'wechat' | 'feishu' | 'webhook') => {
    setTypeFilter(type)
    setCurrentPage(1)
    setSelectedChannels([])
  }

  // 只有在应用未水合时显示加载状态
  if (!isHydrated) {
    return (
      <AppLayout title={t('title')}>
        <div className="flex items-center justify-center h-full">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </AppLayout>
    )
  }

  return (
    <AppLayout title={t('title')}>
      <div className="p-4 space-y-4">
        {/* 工具栏 */}
        <ChannelToolbar
          searchQuery={searchQuery}
          onSearchChange={handleSearchChange}
          statusFilter={statusFilter}
          onStatusFilterChange={handleStatusFilterChange}
          typeFilter={typeFilter}
          onTypeFilterChange={handleTypeFilterChange}
          onCreateChannel={() => setCreateModalOpen(true)}
        />

        {/* 渠道列表 */}
        <ChannelList
          channels={paginatedChannels}
          selectedChannels={selectedChannels}
          onSelectionChange={setSelectedChannels}
          onToggleStatus={handleToggleStatus}
          onDeleteChannel={handleDeleteChannel}
          onUpdateChannel={handleUpdateChannel}
          currentPage={currentPage}
          pageSize={pageSize}
          total={total}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
          onBatchDelete={handleBatchDelete}
          onBatchToggleStatus={handleBatchToggleStatus}
        />

        {/* 创建渠道模态框 */}
        <ChannelForm
          open={createModalOpen}
          onOpenChange={setCreateModalOpen}
          channel={null}
          onSubmit={handleCreateChannel}
          onCancel={() => setCreateModalOpen(false)}
        />
      </div>
    </AppLayout>
  )
}