'use client'

import { ReactNode } from 'react'
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar'
import { AppSidebar } from '@/components/app-sidebar'
import { TopNavBar } from '@/components/header'
import type { BreadcrumbItem } from '@/types/breadcrumb'

interface AppLayoutProps {
  children: ReactNode
  title?: string
  breadcrumbItems?: BreadcrumbItem[]
  showSearch?: boolean
}

export function AppLayout({ 
  children, 
  title, 
  breadcrumbItems, 
  showSearch = true 
}: AppLayoutProps) {
  return (
    <SidebarProvider defaultOpen={true}>
      <AppSidebar />
      <SidebarInset>
        {/* 顶部导航栏 */}
        <TopNavBar 
          title={title}
          breadcrumbItems={breadcrumbItems}
          showSearch={showSearch}
        />

        {/* 主内容 */}
        <main className="flex-1 overflow-y-auto">
          <div className="h-full">
            {children}
          </div>
        </main>
      </SidebarInset>
    </SidebarProvider>
  )
}