'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON>alogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { CheckCircle, XCircle, Clock, Send } from 'lucide-react'
import { useClientTranslation } from '@/hooks/useClientTranslation'
import type { NotificationChannel } from '@/types/data'

interface ChannelTestDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  channel: NotificationChannel | null
}

interface TestResult {
  success: boolean
  message: string
  response?: string
  timestamp: string
}

export function ChannelTestDialog({ open, onOpenChange, channel }: ChannelTestDialogProps) {
  const { t } = useClientTranslation('channels')
  const [testData, setTestData] = useState({
    title: '测试标题',
    content: '这是一条测试消息，用于验证渠道配置是否正确',
    level: 'info',
    message: '系统运行正常',
    data: '{"user": "test_user", "action": "login", "ip": "***********"}'
  })
  const [isTesting, setIsTesting] = useState(false)
  const [testResult, setTestResult] = useState<TestResult | null>(null)
  const [testHistory, setTestHistory] = useState<TestResult[]>([])

  const handleTest = async () => {
    if (!channel) return

    setIsTesting(true)
    setTestResult(null)

    // 模拟测试请求
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 模拟测试结果
    const success = Math.random() > 0.2 // 80% 成功率
    const result: TestResult = {
      success,
      message: success ? '测试消息发送成功' : '测试消息发送失败：连接超时',
      response: success ? 'HTTP 200 OK' : 'HTTP 408 Request Timeout',
      timestamp: new Date().toISOString()
    }

    setTestResult(result)
    setTestHistory(prev => [result, ...prev.slice(0, 4)]) // 保留最近5条记录
    setIsTesting(false)
  }

  const renderPreview = () => {
    if (!channel?.config.template) return null

    let preview = channel.config.template
    
    // 替换变量
    const variables = {
      title: testData.title,
      content: testData.content,
      level: testData.level,
      message: testData.message,
      data: testData.data,
      now: new Date().toLocaleString('zh-CN'),
      source: 'rayin-push'
    }

    Object.entries(variables).forEach(([key, value]) => {
      preview = preview.replace(new RegExp(`\\$\\{${key}\\}`, 'g'), value)
    })

    return preview
  }

  if (!channel) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Send className="h-5 w-5" />
            {t('testChannel')} - {channel.name}
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 左侧：测试配置 */}
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">测试数据配置</CardTitle>
                <CardDescription>
                  配置测试时使用的变量值
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-2">
                  <Label htmlFor="title">标题 (title)</Label>
                  <Input
                    id="title"
                    value={testData.title}
                    onChange={(e) => setTestData(prev => ({ ...prev, title: e.target.value }))}
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="content">内容 (content)</Label>
                  <Textarea
                    id="content"
                    value={testData.content}
                    onChange={(e) => setTestData(prev => ({ ...prev, content: e.target.value }))}
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-2 gap-2">
                  <div className="grid gap-2">
                    <Label htmlFor="level">级别 (level)</Label>
                    <Input
                      id="level"
                      value={testData.level}
                      onChange={(e) => setTestData(prev => ({ ...prev, level: e.target.value }))}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="message">消息 (message)</Label>
                    <Input
                      id="message"
                      value={testData.message}
                      onChange={(e) => setTestData(prev => ({ ...prev, message: e.target.value }))}
                    />
                  </div>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="data">数据 (data)</Label>
                  <Textarea
                    id="data"
                    value={testData.data}
                    onChange={(e) => setTestData(prev => ({ ...prev, data: e.target.value }))}
                    rows={3}
                    className="font-mono text-sm"
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">渠道信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex items-center gap-2">
                  <Badge variant="outline">{channel.type}</Badge>
                  <Badge variant={channel.status === 'active' ? 'default' : 'secondary'}>
                    {channel.status}
                  </Badge>
                </div>
                <div className="text-sm text-gray-600">
                  <div><strong>URL:</strong> {channel.config.url}</div>
                  {channel.config.method && (
                    <div><strong>方法:</strong> {channel.config.method}</div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 右侧：预览和结果 */}
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">消息预览</CardTitle>
                <CardDescription>
                  根据当前测试数据生成的最终消息
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 rounded-lg p-4 whitespace-pre-wrap font-mono text-sm border">
                  {renderPreview() || '无模板配置'}
                </div>
              </CardContent>
            </Card>

            {/* 测试结果 */}
            {testResult && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    {testResult.success ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500" />
                    )}
                    测试结果
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Alert className={testResult.success ? 'border-green-200' : 'border-red-200'}>
                    <AlertDescription>
                      <div className="space-y-2">
                        <div>{testResult.message}</div>
                        {testResult.response && (
                          <div className="text-sm text-gray-600">
                            响应: {testResult.response}
                          </div>
                        )}
                        <div className="text-xs text-gray-500">
                          {new Date(testResult.timestamp).toLocaleString()}
                        </div>
                      </div>
                    </AlertDescription>
                  </Alert>
                </CardContent>
              </Card>
            )}

            {/* 测试历史 */}
            {testHistory.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">{t('testHistory')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {testHistory.map((result, index) => (
                      <div key={index} className="flex items-center gap-2 text-sm p-2 bg-gray-50 rounded">
                        {result.success ? (
                          <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                        ) : (
                          <XCircle className="h-4 w-4 text-red-500 flex-shrink-0" />
                        )}
                        <div className="flex-1">
                          <div>{result.message}</div>
                          <div className="text-xs text-gray-500">
                            {new Date(result.timestamp).toLocaleString()}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        <Separator />

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
          <Button onClick={handleTest} disabled={isTesting}>
            {isTesting ? (
              <>
                <Clock className="h-4 w-4 mr-2 animate-spin" />
                测试中...
              </>
            ) : (
              <>
                <Send className="h-4 w-4 mr-2" />
                发送测试
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}