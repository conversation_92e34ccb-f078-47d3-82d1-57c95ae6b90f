'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Search, Plus } from 'lucide-react'
import { useClientTranslation } from '@/hooks/useClientTranslation'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface ChannelToolbarProps {
  searchQuery: string
  onSearchChange: (query: string) => void
  statusFilter: 'all' | 'active' | 'inactive'
  onStatusFilterChange: (status: 'all' | 'active' | 'inactive') => void
  typeFilter: 'all' | 'wechat' | 'feishu' | 'webhook'
  onTypeFilterChange: (type: 'all' | 'wechat' | 'feishu' | 'webhook') => void
  onCreateChannel: () => void
}

export function ChannelToolbar({
  searchQuery,
  onSearchChange,
  statusFilter,
  onStatusFilterChange,
  typeFilter,
  onTypeFilterChange,
  onCreateChannel
}: ChannelToolbarProps) {
  const { t } = useClientTranslation('channels')

  return (
    <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
      {/* 左侧搜索和筛选 */}
      <div className="flex flex-col sm:flex-row gap-3 flex-1 max-w-2xl">
        {/* 搜索框 */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索渠道名称或描述..."
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* 状态筛选 */}
        <Select value={statusFilter} onValueChange={onStatusFilterChange}>
          <SelectTrigger className="w-[140px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部状态</SelectItem>
            <SelectItem value="active">活跃</SelectItem>
            <SelectItem value="inactive">停用</SelectItem>
          </SelectContent>
        </Select>

        {/* 类型筛选 */}
        <Select value={typeFilter} onValueChange={onTypeFilterChange}>
          <SelectTrigger className="w-[160px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部类型</SelectItem>
            <SelectItem value="wechat">微信机器人</SelectItem>
            <SelectItem value="feishu">飞书机器人</SelectItem>
            <SelectItem value="webhook">自定义Webhook</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* 右侧操作按钮 */}
      <div className="flex gap-2 items-center">
        {/* 创建渠道按钮 */}
        <Button onClick={onCreateChannel} className="cursor-pointer">
          <Plus className="h-4 w-4 mr-1" />
          {t('createChannel')}
        </Button>
      </div>
    </div>
  )
}