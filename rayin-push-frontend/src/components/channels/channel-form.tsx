'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { useClientTranslation } from '@/hooks/useClientTranslation'
import type { NotificationChannel } from '@/types/data'

interface ChannelFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  channel?: NotificationChannel | null
  onSubmit: (data: Partial<NotificationChannel>) => void
  onCancel: () => void
}

const BUILT_IN_TEMPLATES = {
  wechat: {
    simple: '**${title}**\n> ${content}\n> 时间: ${now}',
    detailed: '**${title}**\n> ${content}\n> 数据: ${data}\n> 时间: ${now}\n> 来源: ${source}',
    alert: '🚨 **告警通知**\n**级别**: ${level}\n**消息**: ${message}\n**时间**: ${now}'
  },
  feishu: {
    simple: '**${title}**\n${content}\n\n时间: ${now}',
    detailed: '**${title}**\n${content}\n\n📊 数据: ${data}\n🕐 时间: ${now}\n📍 来源: ${source}',
    alert: '🚨 **告警通知**\n**级别**: ${level}\n**消息**: ${message}\n**时间**: ${now}\n**影响**: ${impact}'
  },
  webhook: {
    json: '{"title": "${title}", "content": "${content}", "data": ${data}, "timestamp": "${now}"}',
    form: 'title=${title}&content=${content}&timestamp=${now}',
    custom: ''
  }
}

export function ChannelForm({ open, onOpenChange, channel, onSubmit, onCancel }: ChannelFormProps) {
  const { t } = useClientTranslation('channels')
  const [formData, setFormData] = useState({
    name: '',
    type: 'wechat' as 'wechat' | 'feishu' | 'webhook',
    description: '',
    config: {
      url: '',
      method: 'POST' as 'GET' | 'POST',
      headers: {} as Record<string, string>,
      template: ''
    }
  })
  const [selectedTemplate, setSelectedTemplate] = useState('simple')
  const [customHeaders, setCustomHeaders] = useState('')

  useEffect(() => {
    if (channel) {
      setFormData({
        name: channel.name,
        type: channel.type,
        description: channel.description,
        config: {
          url: channel.config.url || '',
          method: channel.config.method || 'POST',
          headers: channel.config.headers || {},
          template: channel.config.template || ''
        }
      })
      setCustomHeaders(
        Object.entries(channel.config.headers || {})
          .map(([key, value]) => `${key}: ${value}`)
          .join('\n')
      )
    } else {
      // Reset form for new channel
      setFormData({
        name: '',
        type: 'wechat',
        description: '',
        config: {
          url: '',
          method: 'POST',
          headers: {},
          template: BUILT_IN_TEMPLATES.wechat.simple
        }
      })
      setSelectedTemplate('simple')
      setCustomHeaders('')
    }
  }, [channel, open])

  const handleTypeChange = (type: 'wechat' | 'feishu' | 'webhook') => {
    setFormData(prev => ({
      ...prev,
      type,
      config: {
        ...prev.config,
        template: BUILT_IN_TEMPLATES[type].simple,
        method: type === 'webhook' ? 'POST' : 'POST'
      }
    }))
    setSelectedTemplate('simple')
  }

  const handleTemplateChange = (templateKey: string) => {
    setSelectedTemplate(templateKey)
    if (templateKey !== 'custom') {
      const template = BUILT_IN_TEMPLATES[formData.type][templateKey as keyof typeof BUILT_IN_TEMPLATES[typeof formData.type]]
      setFormData(prev => ({
        ...prev,
        config: {
          ...prev.config,
          template
        }
      }))
    }
  }

  const handleHeadersChange = (value: string) => {
    setCustomHeaders(value)
    const headers: Record<string, string> = {}
    value.split('\n').forEach(line => {
      const [key, ...valueParts] = line.split(':')
      if (key && valueParts.length > 0) {
        headers[key.trim()] = valueParts.join(':').trim()
      }
    })
    setFormData(prev => ({
      ...prev,
      config: {
        ...prev.config,
        headers
      }
    }))
  }

  const handleSubmit = () => {
    onSubmit(formData)
  }

  const renderTemplateOptions = () => {
    const templates = BUILT_IN_TEMPLATES[formData.type]
    return Object.keys(templates).map(key => (
      <SelectItem key={key} value={key}>
        {key === 'simple' && '简单模板'}
        {key === 'detailed' && '详细模板'}
        {key === 'alert' && '告警模板'}
        {key === 'json' && 'JSON格式'}
        {key === 'form' && '表单格式'}
        {key === 'custom' && '自定义'}
      </SelectItem>
    ))
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {channel ? t('editChannel') : t('createChannel')}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* 基本信息 */}
          <div className="grid gap-4">
            <div className="grid gap-2">
              <Label htmlFor="name">{t('channelName')}</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="输入渠道名称"
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="type">{t('channelType')}</Label>
              <Select value={formData.type} onValueChange={handleTypeChange}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="wechat">{t('wechatBot')}</SelectItem>
                  <SelectItem value="feishu">{t('feishuBot')}</SelectItem>
                  <SelectItem value="webhook">{t('customWebhook')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="description">{t('channelDescription')}</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="描述该渠道的用途和配置信息"
                rows={3}
              />
            </div>
          </div>

          <Separator />

          {/* 渠道配置 */}
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="basic">基础配置</TabsTrigger>
              <TabsTrigger value="template">消息模板</TabsTrigger>
              {formData.type === 'webhook' && (
                <TabsTrigger value="advanced">高级配置</TabsTrigger>
              )}
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="url">{t('webhookUrl')}</Label>
                <Input
                  id="url"
                  value={formData.config.url}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    config: { ...prev.config, url: e.target.value }
                  }))}
                  placeholder="https://example.com/webhook"
                />
              </div>

              {formData.type === 'webhook' && (
                <div className="grid gap-2">
                  <Label htmlFor="method">{t('httpMethod')}</Label>
                  <Select
                    value={formData.config.method}
                    onValueChange={(value: 'GET' | 'POST') =>
                      setFormData(prev => ({
                        ...prev,
                        config: { ...prev.config, method: value }
                      }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="GET">GET</SelectItem>
                      <SelectItem value="POST">POST</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
            </TabsContent>

            <TabsContent value="template" className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="template-select">{t('builtinTemplate')}</Label>
                <Select value={selectedTemplate} onValueChange={handleTemplateChange}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {renderTemplateOptions()}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="template">{t('messageTemplate')}</Label>
                <Textarea
                  id="template"
                  value={formData.config.template}
                  onChange={(e) => {
                    setFormData(prev => ({
                      ...prev,
                      config: { ...prev.config, template: e.target.value }
                    }))
                    setSelectedTemplate('custom')
                  }}
                  placeholder="输入消息模板，支持变量如 ${title}, ${content}, ${now} 等"
                  rows={8}
                  className="font-mono text-sm"
                />
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">{t('supportedVariables')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div><Badge variant="outline">${'title'}</Badge> - 标题</div>
                    <div><Badge variant="outline">${'content'}</Badge> - 内容</div>
                    <div><Badge variant="outline">${'data'}</Badge> - 原始数据</div>
                    <div><Badge variant="outline">${'now'}</Badge> - 当前时间</div>
                    <div><Badge variant="outline">${'level'}</Badge> - 告警级别</div>
                    <div><Badge variant="outline">${'message'}</Badge> - 消息内容</div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {formData.type === 'webhook' && (
              <TabsContent value="advanced" className="space-y-4">
                <div className="grid gap-2">
                  <Label htmlFor="headers">{t('requestHeaders')}</Label>
                  <Textarea
                    id="headers"
                    value={customHeaders}
                    onChange={(e) => handleHeadersChange(e.target.value)}
                    placeholder={`Content-Type: application/json\nAuthorization: Bearer token\nX-Custom-Header: value`}
                    rows={6}
                    className="font-mono text-sm"
                  />
                  <p className="text-sm text-gray-500">
                    每行一个请求头，格式为 &quot;key: value&quot;
                  </p>
                </div>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">常用请求头示例</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-1 text-sm font-mono">
                      <div>Content-Type: application/json</div>
                      <div>Authorization: Bearer ${'${token}'}</div>
                      <div>X-Source: rayin-push</div>
                      <div>User-Agent: RayinPush/1.0</div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            )}
          </Tabs>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onCancel}>
            取消
          </Button>
          <Button onClick={handleSubmit}>
            {channel ? '更新' : '创建'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}