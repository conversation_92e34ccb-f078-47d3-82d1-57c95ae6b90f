'use client'

import { useMemo } from 'react'
import {
  Side<PERSON>,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from '@/components/ui/sidebar'
import Image from 'next/image'
import { NavGroup } from '@/components/layout/nav-group'
import { NavUser } from '@/components/layout/nav-user'

import { sidebarData } from '@/components/layout/data/sidebar-data'
import { navigationConfig, filterNavigationByRole } from '@/config/navigation'
import { useAuthStore } from '@/stores'
import { useTypedTranslation } from '@/hooks/useTypedTranslation'
import type { NavigationTranslations } from '@/types/i18n'

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { user, isAuthenticated } = useAuthStore()
  const { t } = useTypedTranslation('navigation')

  // Use actual user data if available, otherwise use default
  const currentUser = user || sidebarData.user

  // 过滤导航项根据用户权限，使用 useMemo 缓存
  const filteredNavigation = useMemo(
    () => filterNavigationByRole(navigationConfig, user?.role, isAuthenticated),
    [user?.role, isAuthenticated]
  )

  // 转换导航配置格式以适配新的侧边栏，使用国际化和 useMemo 缓存
  const navGroups = useMemo(
    () => filteredNavigation.map((section) => ({
      id: section.id,
      title: section.titleKey ? t(section.titleKey as keyof NavigationTranslations) : section.title,
      items: section.items.map((item) => ({
        title: item.titleKey ? t(item.titleKey as keyof NavigationTranslations) : item.title,
        url: item.href,
        icon: item.icon,
      }))
    })),
    [filteredNavigation, t]
  )

  return (
    <Sidebar collapsible="icon" variant="floating" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground">
              <div className="flex aspect-square size-8 items-center justify-center">
                <Image
                  src="/logo.svg"
                  alt="Rayin Push Logo"
                  width={28}
                  height={28}
                />
              </div>
              <div className="grid flex-1 text-left leading-tight">
                <span className="truncate font-semibold text-lg bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
                  Rayin Push
                </span>
              </div>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        {navGroups.map((group) => (
          <NavGroup key={group.id} title={group.title} items={group.items} />
        ))}
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={currentUser} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}