'use client'

import { useEffect, useMemo } from 'react'
import { X, Menu, ChevronLeft, ChevronRight } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { useSidebar } from '@/hooks/useStore'
import { useAuthStore } from '@/stores'
import { NavigationItemComponent } from '@/components/navigation-item'
import { useTypedTranslation } from '@/hooks/useTypedTranslation'
import { navigationConfig, filterNavigationByRole } from '@/config/navigation'
import type { CommonTranslations } from '@/types/i18n'

export function Sidebar() {
  const { t } = useTypedTranslation('common')
  const {
    isOpen,
    isCollapsed,
    isMobile,
    toggleSidebar,
    toggleSidebarCollapse,
    setSidebarOpen
  } = useSidebar()
  
  const { user, isAuthenticated } = useAuthStore()

  // 过滤导航项根据用户权限，使用 useMemo 缓存
  const filteredNavigation = useMemo(
    () => filterNavigationByRole(navigationConfig, user?.role, isAuthenticated),
    [user?.role, isAuthenticated]
  )

  // 移动端时点击遮罩关闭侧边栏
  const handleOverlayClick = () => {
    if (isMobile) {
      setSidebarOpen(false)
    }
  }

  // ESC键关闭侧边栏
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isMobile && isOpen) {
        setSidebarOpen(false)
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [isMobile, isOpen, setSidebarOpen])

  return (
    <>
      {/* 移动端遮罩 */}
      {isMobile && isOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black/50 lg:hidden"
          onClick={handleOverlayClick}
        />
      )}

      {/* 侧边栏 */}
      <aside
        className={cn(
          "h-full bg-background border-r border-border transition-all duration-300 ease-in-out",
          "flex flex-col",
          // 桌面端样式
          "relative",
          // 宽度控制
          isCollapsed && !isMobile ? "w-16" : "w-64",
          // 移动端时使用固定定位和transform
          isMobile && "fixed top-0 left-0 z-50",
          isMobile && (isOpen ? "translate-x-0" : "-translate-x-full")
        )}
      >
        {/* 侧边栏头部 */}
        <div className={cn(
          "flex items-center border-b border-border",
          isCollapsed && !isMobile ? "h-16 px-4 justify-center" : "h-16 px-6 justify-between"
        )}>
          {(!isCollapsed || isMobile) && (
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary rounded-md flex items-center justify-center">
                <span className="text-primary-foreground font-bold text-sm">R</span>
              </div>
              <span className="font-semibold text-lg">Rayin Push</span>
            </div>
          )}

          {/* 移动端关闭按钮 */}
          {isMobile && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarOpen(false)}
              className="lg:hidden"
            >
              <X className="h-4 w-4" />
            </Button>
          )}

          {/* 桌面端折叠按钮 */}
          {!isMobile && (
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleSidebarCollapse}
              className={cn(
                "hidden lg:flex",
                isCollapsed && "w-8 h-8 p-0"
              )}
            >
              {isCollapsed ? (
                <ChevronRight className="h-4 w-4" />
              ) : (
                <ChevronLeft className="h-4 w-4" />
              )}
            </Button>
          )}
        </div>

        {/* 导航菜单 */}
        <nav className="flex-1 overflow-y-auto p-4 space-y-6">
          {filteredNavigation.map((section) => (
            <div key={section.id}>
              {/* 分组标题 */}
              {section.title && !isCollapsed && (
                <h3 className="px-3 mb-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                  {section.titleKey ? t(section.titleKey as keyof CommonTranslations) : section.title}
                </h3>
              )}

              {/* 分组分隔线（折叠时） */}
              {section.title && isCollapsed && !isMobile && (
                <div className="mx-3 mb-2 border-t border-border" />
              )}

              {/* 菜单项 */}
              <div className="space-y-1">
                {section.items.map((item) => (
                  <NavigationItemComponent
                    key={item.id}
                    item={item}
                    isCollapsed={isCollapsed && !isMobile}
                  />
                ))}
              </div>
            </div>
          ))}
        </nav>

        {/* 侧边栏底部 */}
        <div className={cn(
          "border-t border-border p-4",
          isCollapsed && !isMobile && "px-2"
        )}>
          {(!isCollapsed || isMobile) && user && (
            <div className="flex items-center space-x-3 p-2 rounded-md bg-accent/50">
              <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                <span className="text-primary-foreground text-sm font-medium">
                  {user.username.charAt(0).toUpperCase()}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{user.username}</p>
                <p className="text-xs text-muted-foreground truncate">{user.email}</p>
              </div>
            </div>
          )}

          {isCollapsed && !isMobile && user && (
            <div className="flex justify-center">
              <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                <span className="text-primary-foreground text-sm font-medium">
                  {user.username.charAt(0).toUpperCase()}
                </span>
              </div>
            </div>
          )}
        </div>
      </aside>
    </>
  )
}