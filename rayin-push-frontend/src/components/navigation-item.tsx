'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { ChevronDown, ChevronRight } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useTypedTranslation } from '@/hooks/useTypedTranslation'
import { useLocale } from '@/hooks/useLocale'
import type { NavigationItem } from '@/types/navigation'
import type { CommonTranslations } from '@/types/i18n'

interface NavigationItemProps {
  item: NavigationItem
  isCollapsed: boolean
  level?: number
}

export function NavigationItemComponent({ 
  item, 
  isCollapsed, 
  level = 0 
}: NavigationItemProps) {
  const pathname = usePathname()
  const { locale } = useLocale()
  const { t } = useTypedTranslation('common')
  const [isExpanded, setIsExpanded] = useState(false)

  // 构建带 locale 前缀的链接
  const localizedHref = `/${locale}${item.href}`

  const isActive = pathname === localizedHref || pathname.startsWith(localizedHref + '/')
  const hasChildren = item.children && item.children.length > 0
  const shouldShowChildren = hasChildren && isExpanded && !isCollapsed

  const handleToggle = () => {
    if (hasChildren) {
      setIsExpanded(!isExpanded)
    }
  }

  const itemContent = (
    <>
      <div className="flex items-center flex-1 min-w-0">
        <item.icon className={cn(
          "flex-shrink-0",
          isCollapsed ? "h-5 w-5" : "h-4 w-4 mr-3"
        )} />
        {!isCollapsed && (
          <span className="truncate text-sm font-medium">
            {item.titleKey ? t(item.titleKey as keyof CommonTranslations) : item.title}
          </span>
        )}
      </div>
      
      {!isCollapsed && item.badge && (
        <span className="ml-auto flex-shrink-0 bg-primary text-primary-foreground text-xs rounded-full px-2 py-0.5">
          {item.badge}
        </span>
      )}
      
      {!isCollapsed && hasChildren && (
        <button
          onClick={handleToggle}
          className="ml-auto flex-shrink-0 p-1 hover:bg-accent rounded cursor-pointer"
        >
          {isExpanded ? (
            <ChevronDown className="h-3 w-3" />
          ) : (
            <ChevronRight className="h-3 w-3" />
          )}
        </button>
      )}
    </>
  )

  const itemClasses = cn(
    "flex items-center w-full px-3 py-2 text-left transition-colors rounded-md",
    "hover:bg-accent hover:text-accent-foreground",
    "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring",
    isActive && "bg-accent text-accent-foreground",
    isCollapsed ? "justify-center" : "justify-start",
    level > 0 && !isCollapsed && "ml-4 border-l border-border pl-4"
  )

  return (
    <div>
      {hasChildren && !isCollapsed ? (
        <button
          onClick={handleToggle}
          className={itemClasses}
          title={isCollapsed ? (item.titleKey ? t(item.titleKey as keyof CommonTranslations) : item.title) : undefined}
        >
          {itemContent}
        </button>
      ) : (
        <Link
          href={localizedHref}
          className={itemClasses}
          title={isCollapsed ? (item.titleKey ? t(item.titleKey as keyof CommonTranslations) : item.title) : undefined}
        >
          {itemContent}
        </Link>
      )}

      {shouldShowChildren && (
        <div className="mt-1 space-y-1">
          {item.children!.map((child) => (
            <NavigationItemComponent
              key={child.id}
              item={child}
              isCollapsed={isCollapsed}
              level={level + 1}
            />
          ))}
        </div>
      )}
    </div>
  )
}